
import time
import argparse
import os
import subprocess
import sys
import tempfile
import shutil
from pathlib import Path
from stem import Signal
from stem.control import Controller
from stem.connection import connect
from tenacity import retry, stop_after_attempt, wait_fixed

# 全局配置变量 - 可直接修改这里的值
DEFAULT_EXIT_NODES = None  # 例如: "{us},{ca},{gb}" 或 "{jp},{sg}"
DEFAULT_ENTRY_NODES = None  # 例如: "{us},{ca},{gb}" 或 "{jp},{sg}"
DEFAULT_CONTROL_PORT = 9051  # 双重用途：1) 动态设置torrc的ControlPort 2) 连接到当前控制端口
CLEAR_DNS_CACHE = True  # 是否在切换时清除 DNS 缓存

# 常用配置示例（取消注释使用）:
# DEFAULT_EXIT_NODES = "{us}"          # 美国出口
# DEFAULT_ENTRY_NODES = "{us}"         # 美国入口
# DEFAULT_CONTROL_PORT = 9050          # 双重用途：动态配置+连接端口
# CLEAR_DNS_CACHE = False              # 不清除DNS缓存

# DEFAULT_EXIT_NODES = "{gb},{de},{fr}"  # 欧洲出口
# DEFAULT_ENTRY_NODES = "{gb},{de},{fr}" # 欧洲入口
# DEFAULT_CONTROL_PORT = 9051             # 双重用途：动态配置+连接端口
# CLEAR_DNS_CACHE = True                  # 清除DNS缓存

def find_tor_executable():
    """查找Tor可执行文件路径"""
    possible_paths = [
        # Windows常见路径
        r"C:\Program Files\Tor Browser\Browser\TorBrowser\Tor\tor.exe",
        r"C:\Program Files (x86)\Tor Browser\Browser\TorBrowser\Tor\tor.exe",
        r"C:\Users\<USER>\Desktop\Tor Browser\Browser\TorBrowser\Tor\tor.exe".format(os.getenv('USERNAME', '')),
        r"C:\tor\tor.exe",
        r"C:\Program Files\Tor\tor.exe",
        r"C:\Program Files (x86)\Tor\tor.exe",
        # 系统PATH中的tor
        "tor.exe",
        "tor"
    ]

    for path in possible_paths:
        try:
            if os.path.isfile(path):
                print(f"找到Tor可执行文件: {path}")
                return path
        except:
            continue

    # 尝试在PATH中查找
    try:
        result = subprocess.run(['where', 'tor'], capture_output=True, text=True, shell=True)
        if result.returncode == 0:
            tor_path = result.stdout.strip().split('\n')[0]
            print(f"在PATH中找到Tor: {tor_path}")
            return tor_path
    except:
        pass

    return None

def create_torrc_config(control_port=9051, data_dir=None):
    """创建临时torrc配置文件"""
    if data_dir is None:
        data_dir = tempfile.mkdtemp(prefix="tor_data_")

    torrc_content = f"""# 临时Tor配置文件
# 数据目录
DataDirectory {data_dir}

# 控制端口配置
ControlPort {control_port}
ControlListenAddress 127.0.0.1

# 禁用控制端口密码（仅本地使用）
# HashedControlPassword 留空表示无密码

# SOCKS端口
SocksPort 9050
SocksListenAddress 127.0.0.1

# 日志配置
Log notice stdout
Log notice file {os.path.join(data_dir, 'tor.log')}

# 安全配置
CookieAuthentication 0
DisableDebuggerAttachment 0

# 网络配置
ClientOnly 1
"""

    torrc_path = os.path.join(data_dir, 'torrc')
    with open(torrc_path, 'w', encoding='utf-8') as f:
        f.write(torrc_content)

    print(f"创建torrc配置文件: {torrc_path}")
    print(f"数据目录: {data_dir}")
    return torrc_path, data_dir

def start_tor_with_config(control_port=9051):
    """启动带有控制端口配置的Tor进程"""
    print(f"正在启动Tor进程 (控制端口: {control_port})...")

    # 查找Tor可执行文件
    tor_exe = find_tor_executable()
    if not tor_exe:
        print("❌ 未找到Tor可执行文件!")
        print("请确保已安装Tor Browser或独立的Tor程序")
        print("常见安装位置:")
        print("- Tor Browser: https://www.torproject.org/download/")
        print("- 独立Tor: https://dist.torproject.org/")
        return None, None

    # 创建临时配置
    torrc_path, data_dir = create_torrc_config(control_port)

    try:
        # 启动Tor进程
        cmd = [tor_exe, '-f', torrc_path]
        print(f"执行命令: {' '.join(cmd)}")

        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0
        )

        print(f"Tor进程已启动 (PID: {process.pid})")
        print("等待Tor启动完成...")

        # 等待Tor启动完成
        for i in range(30):  # 最多等待30秒
            try:
                with Controller.from_port(port=control_port) as controller:
                    controller.authenticate()
                    print("✅ Tor启动成功，控制端口可用!")
                    return process, data_dir
            except:
                print(f"等待Tor启动... ({i+1}/30)")
                time.sleep(1)

        print("❌ Tor启动超时")
        process.terminate()
        return None, None

    except Exception as e:
        print(f"❌ 启动Tor失败: {e}")
        return None, None

def check_tor_connection(control_port=9051):
    """检查Tor控制端口连接"""
    try:
        with Controller.from_port(port=control_port) as controller:
            controller.authenticate()
            return True
    except Exception as e:
        print(f"无法连接到Tor控制端口 {control_port}: {e}")
        return False

def countdown_sleep(seconds, message="等待中"):
    """带倒计时显示的睡眠函数"""
    print(f"{message}: ", end="", flush=True)
    for i in range(int(seconds), 0, -1):
        print(f"{i}秒", end="", flush=True)
        time.sleep(1)
        if i > 1:
            print("...", end="", flush=True)
    print(" 完成!")

    # 处理小数部分
    remaining = seconds - int(seconds)
    if remaining > 0:
        time.sleep(remaining)

@retry(stop=stop_after_attempt(3), wait=wait_fixed(2))
def switch_tor_identity(control_port=9051, exit_nodes=None, entry_nodes=None, clear_dns=True, auto_start=True):
    """切换Tor身份 - 统一版本，支持自动启动Tor"""
    print(f"开始切换Tor身份 (端口: {control_port})...")

    tor_process = None
    data_dir = None

    try:
        # 检查Tor连接，如果失败且允许自动启动，则启动Tor
        if not check_tor_connection(control_port):
            if auto_start:
                print("Tor未运行，尝试自动启动...")
                tor_process, data_dir = start_tor_with_config(control_port)
                if not tor_process:
                    raise Exception("无法启动Tor进程")
            else:
                raise Exception(f"无法连接到Tor控制端口 {control_port}")

        with Controller.from_port(port=control_port) as controller:
            print(f"连接到Tor控制端口{control_port}...")
            controller.authenticate()
            print("认证成功")

            # 双重用途：动态设置 torrc 的 ControlPort 配置
            print(f"动态设置 torrc ControlPort 为: {control_port}")
            controller.set_conf('ControlPort', str(control_port))

            # 配置区域节点
            if exit_nodes:
                print(f"设置出口节点区域: {exit_nodes}")
                controller.set_conf('ExitNodes', exit_nodes)

            if entry_nodes:
                print(f"设置入口节点区域: {entry_nodes}")
                controller.set_conf('EntryNodes', entry_nodes)

            # 关键修复1: 关闭现有电路
            circuits = controller.get_circuits()
            print(f"找到 {len(circuits)} 个电路")
            closed_count = 0
            for circuit in circuits:
                if circuit.status == 'BUILT':
                    try:
                        controller.close_circuit(circuit.id)
                        closed_count += 1
                    except Exception as e:
                        print(f"关闭电路 {circuit.id} 失败: {e}")
            print(f"成功关闭 {closed_count} 个电路")

            # 关键修复2: 清除DNS缓存（如果启用）
            if clear_dns:
                print("清除DNS缓存...")
                controller.signal(Signal.CLEARDNSCACHE)

            # 关键修复3: 发送NEWNYM信号
            print("发送NEWNYM信号...")
            controller.signal(Signal.NEWNYM)

            # 关键修复4: 必须等待推荐时间
            wait_time = controller.get_newnym_wait()
            if wait_time > 0:
                countdown_sleep(wait_time, f"等待推荐时间({wait_time}秒)")
            else:
                print("无需等待推荐时间")

            # 关键修复5: 额外等待确保电路建立
            countdown_sleep(5, "额外等待确保电路建立")
            print(f"端口{control_port} Tor身份切换完成")

    except Exception as e:
        # 如果是我们启动的Tor进程，在出错时清理
        if tor_process:
            print("清理自动启动的Tor进程...")
            tor_process.terminate()
            if data_dir and os.path.exists(data_dir):
                try:
                    shutil.rmtree(data_dir)
                    print(f"清理临时目录: {data_dir}")
                except:
                    pass
        raise e

# 旧函数已合并到 switch_tor_identity 统一函数中

def main():
    """主函数，支持命令行参数和全局变量"""
    parser = argparse.ArgumentParser(description='Tor身份切换工具')
    parser.add_argument('--exit-nodes', type=str, help='出口节点区域 (例如: {us},{ca},{gb})')
    parser.add_argument('--entry-nodes', type=str, help='入口节点区域 (例如: {us},{ca},{gb})')
    parser.add_argument('--control-port', type=int, help='Tor控制端口 (例如: 9050, 9051)')
    parser.add_argument('--clear-dns', action='store_true', help='清除DNS缓存')
    parser.add_argument('--no-clear-dns', action='store_true', help='不清除DNS缓存')

    args = parser.parse_args()

    # 优先级: 命令行参数 > 全局变量 > 默认值
    exit_nodes = args.exit_nodes or DEFAULT_EXIT_NODES
    entry_nodes = args.entry_nodes or DEFAULT_ENTRY_NODES
    control_port = args.control_port or DEFAULT_CONTROL_PORT

    # DNS缓存清除逻辑
    if args.clear_dns:
        clear_dns = True
    elif args.no_clear_dns:
        clear_dns = False
    else:
        clear_dns = CLEAR_DNS_CACHE

    print("=== Tor身份切换工具 ===")
    print(f"配置来源: {'命令行参数' if any([args.exit_nodes, args.entry_nodes, args.control_port, args.clear_dns, args.no_clear_dns]) else '全局变量'}")
    if exit_nodes:
        print(f"出口节点区域: {exit_nodes}")
    if entry_nodes:
        print(f"入口节点区域: {entry_nodes}")
    print(f"控制端口: {control_port}")
    print(f"清除DNS缓存: {'是' if clear_dns else '否'}")
    print("=" * 25)

    try:
        switch_tor_identity(
            control_port=control_port,
            exit_nodes=exit_nodes,
            entry_nodes=entry_nodes,
            clear_dns=clear_dns
        )

        print("\n✅ 所有操作完成!")

    except Exception as e:
        print(f"\n❌ 操作失败: {e}")
        return 1

    return 0

if __name__ == "__main__":
    exit(main())