
import time
import argparse
from stem import Signal
from stem.control import Controller
from tenacity import retry

# 全局配置变量 - 可直接修改这里的值
DEFAULT_EXIT_NODES = None  # 例如: "{us},{ca},{gb}" 或 "{jp},{sg}"
DEFAULT_ENTRY_NODES = None  # 例如: "{us},{ca},{gb}" 或 "{jp},{sg}"
DEFAULT_CONTROL_PORT = 9051  # 双重用途：1) 动态设置torrc的ControlPort 2) 连接到当前控制端口
CLEAR_DNS_CACHE = True  # 是否在切换时清除 DNS 缓存

# 常用配置示例（取消注释使用）:
# DEFAULT_EXIT_NODES = "{us}"          # 美国出口
# DEFAULT_ENTRY_NODES = "{us}"         # 美国入口
# DEFAULT_CONTROL_PORT = 9050          # 双重用途：动态配置+连接端口
# CLEAR_DNS_CACHE = False              # 不清除DNS缓存

# DEFAULT_EXIT_NODES = "{gb},{de},{fr}"  # 欧洲出口
# DEFAULT_ENTRY_NODES = "{gb},{de},{fr}" # 欧洲入口
# DEFAULT_CONTROL_PORT = 9051             # 双重用途：动态配置+连接端口
# CLEAR_DNS_CACHE = True                  # 清除DNS缓存

def countdown_sleep(seconds, message="等待中"):
    """带倒计时显示的睡眠函数"""
    print(f"{message}: ", end="", flush=True)
    for i in range(int(seconds), 0, -1):
        print(f"{i}秒", end="", flush=True)
        time.sleep(1)
        if i > 1:
            print("...", end="", flush=True)
    print(" 完成!")

    # 处理小数部分
    remaining = seconds - int(seconds)
    if remaining > 0:
        time.sleep(remaining)

@retry
def switch_tor_identity(control_port=9051, exit_nodes=None, entry_nodes=None, clear_dns=True):
    """切换Tor身份 - 统一版本"""
    print(f"开始切换Tor身份 (端口: {control_port})...")

    with Controller.from_port(port=control_port) as controller:
        print(f"连接到Tor控制端口{control_port}...")
        controller.authenticate()
        print("认证成功")

        # 双重用途：动态设置 torrc 的 ControlPort 配置
        print(f"动态设置 torrc ControlPort 为: {control_port}")
        controller.set_conf('ControlPort', str(control_port))
        controller.signal(Signal.CLEARDNSCACHE)

        # 配置区域节点
        if exit_nodes:
            print(f"设置出口节点区域: {exit_nodes}")
            controller.set_conf('ExitNodes', exit_nodes)

        if entry_nodes:
            print(f"设置入口节点区域: {entry_nodes}")
            controller.set_conf('EntryNodes', entry_nodes)

        # 关键修复1: 关闭现有电路
        circuits = controller.get_circuits()
        print(f"找到 {len(circuits)} 个电路")
        closed_count = 0
        for circuit in circuits:
            if circuit.status == 'BUILT':
                try:
                    controller.close_circuit(circuit.id)
                    closed_count += 1
                except Exception as e:
                    print(f"关闭电路 {circuit.id} 失败: {e}")
        print(f"成功关闭 {closed_count} 个电路")

        # 关键修复2: 清除DNS缓存（如果启用）
        if clear_dns:
            print("清除DNS缓存...")
            controller.signal(Signal.CLEARDNSCACHE)

        # 关键修复3: 发送NEWNYM信号
        print("发送NEWNYM信号...")
        controller.signal(Signal.NEWNYM)

        # 关键修复4: 必须等待推荐时间
        wait_time = controller.get_newnym_wait()
        if wait_time > 0:
            countdown_sleep(wait_time, f"等待推荐时间({wait_time}秒)")
        else:
            print("无需等待推荐时间")

        # 关键修复5: 额外等待确保电路建立
        countdown_sleep(5, "额外等待确保电路建立")
        print(f"端口{control_port} Tor身份切换完成")

# 旧函数已合并到 switch_tor_identity 统一函数中

def main():
    """主函数，支持命令行参数和全局变量"""
    parser = argparse.ArgumentParser(description='Tor身份切换工具')
    parser.add_argument('--exit-nodes', type=str, help='出口节点区域 (例如: {us},{ca},{gb})')
    parser.add_argument('--entry-nodes', type=str, help='入口节点区域 (例如: {us},{ca},{gb})')
    parser.add_argument('--control-port', type=int, help='Tor控制端口 (例如: 9050, 9051)')
    parser.add_argument('--clear-dns', action='store_true', help='清除DNS缓存')
    parser.add_argument('--no-clear-dns', action='store_true', help='不清除DNS缓存')

    args = parser.parse_args()

    # 优先级: 命令行参数 > 全局变量 > 默认值
    exit_nodes = args.exit_nodes or DEFAULT_EXIT_NODES
    entry_nodes = args.entry_nodes or DEFAULT_ENTRY_NODES
    control_port = args.control_port or DEFAULT_CONTROL_PORT

    # DNS缓存清除逻辑
    if args.clear_dns:
        clear_dns = True
    elif args.no_clear_dns:
        clear_dns = False
    else:
        clear_dns = CLEAR_DNS_CACHE

    print("=== Tor身份切换工具 ===")
    print(f"配置来源: {'命令行参数' if any([args.exit_nodes, args.entry_nodes, args.control_port, args.clear_dns, args.no_clear_dns]) else '全局变量'}")
    if exit_nodes:
        print(f"出口节点区域: {exit_nodes}")
    if entry_nodes:
        print(f"入口节点区域: {entry_nodes}")
    print(f"控制端口: {control_port}")
    print(f"清除DNS缓存: {'是' if clear_dns else '否'}")
    print("=" * 25)

    try:
        switch_tor_identity(
            control_port=control_port,
            exit_nodes=exit_nodes,
            entry_nodes=entry_nodes,
            clear_dns=clear_dns
        )

        print("\n✅ 所有操作完成!")

    except Exception as e:
        print(f"\n❌ 操作失败: {e}")
        return 1

    return 0

if __name__ == "__main__":
    exit(main())